<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\OBJ\VirtualCOMPort.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\OBJ\VirtualCOMPort.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Wed Jul 30 12:38:55 2025
<BR><P>
<H3>Maximum Stack Usage =         80 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; AD9959_Set_Fre &rArr; Write_CFTW0 &rArr; __aeabi_dmul
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[a2]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1f]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC1_2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3c]">ADC3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[7]">BusFault_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[22]">CAN1_RX1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[23]">CAN1_SCE_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[18]">DMA1_Channel1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[19]">DMA1_Channel2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel6_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel7_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[45]">DMA2_Channel1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[46]">DMA2_Channel2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[47]">DMA2_Channel3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[48]">DMA2_Channel4_5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[a]">DebugMon_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[35]">EXTI15_10_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[24]">EXTI9_5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[11]">FLASH_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3d]">FSMC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[5]">HardFault_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2d]">I2C1_ER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2c]">I2C1_EV_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2f]">I2C2_ER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2e]">I2C2_EV_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[6]">MemManage_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[4]">NMI_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[e]">PVD_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[b]">PendSV_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[12]">RCC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[36]">RTCAlarm_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[10]">RTC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[31]">SPI2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[40]">SPI3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[9]">SVC_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[c]">SysTick_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[49]">SystemInit</a> from system_stm32f10x.o(.text) referenced from startup_stm32f10x_hd.o(.text)
 <LI><a href="#[f]">TAMPER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[25]">TIM1_BRK_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[28]">TIM1_CC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[27]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[26]">TIM1_UP_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[29]">TIM2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2a]">TIM3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2b]">TIM4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3f]">TIM5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[43]">TIM6_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[44]">TIM7_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[38]">TIM8_BRK_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3b]">TIM8_CC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3a]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[39]">TIM8_UP_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[42]">UART5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from usart.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[34]">USART3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[37]">USBWakeUp_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[20]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[21]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[8]">UsageFault_Handler</a> from stm32f10x_it.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[d]">WWDG_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[4d]">__main</a> from __main.o(!!!main) referenced from startup_stm32f10x_hd.o(.text)
 <LI><a href="#[4c]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[4b]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[4d]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[4e]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[50]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[a8]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[a9]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[51]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[aa]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[9e]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[52]"></a>_printf_x</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = _printf_x &rArr; _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[ab]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[57]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[ac]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[ad]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[ae]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[af]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[b0]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[b1]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[b2]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[b3]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[b4]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[b5]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[b6]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[b7]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[b8]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[b9]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[ba]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[bb]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[bc]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[bd]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[be]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[bf]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[c0]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[5c]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[c1]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[c2]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[c3]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[c4]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[c5]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[c6]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[c7]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[4f]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[c8]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[54]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[56]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[c9]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[58]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; AD9959_Set_Fre &rArr; Write_CFTW0 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ca]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[a3]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[5b]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[cb]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[5d]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[73]"></a>WFI_SET</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, sys.o(.emb_text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYS_Standby
</UL>

<P><STRONG><a name="[4]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>SystemInit</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, system_stm32f10x.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SystemInit &rArr; SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(.text)
</UL>
<P><STRONG><a name="[cc]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, system_stm32f10x.o(.text), UNUSED)

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[a2]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[6d]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_PriorityGroup_Config
</UL>

<P><STRONG><a name="[6f]"></a>NVIC_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, misc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_Init
</UL>

<P><STRONG><a name="[cd]"></a>NVIC_SetVectorTable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, misc.o(.text), UNUSED)

<P><STRONG><a name="[ce]"></a>NVIC_SystemLPConfig</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, misc.o(.text), UNUSED)

<P><STRONG><a name="[cf]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, misc.o(.text), UNUSED)

<P><STRONG><a name="[61]"></a>GPIO_DeInit</STRONG> (Thumb, 172 bytes, Stack size 8 bytes, stm32f10x_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>

<P><STRONG><a name="[63]"></a>GPIO_AFIODeInit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f10x_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>

<P><STRONG><a name="[77]"></a>GPIO_Init</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, stm32f10x_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[d0]"></a>GPIO_StructInit</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d2]"></a>GPIO_ReadInputData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d3]"></a>GPIO_ReadOutputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d4]"></a>GPIO_ReadOutputData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d5]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d6]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d7]"></a>GPIO_WriteBit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d8]"></a>GPIO_Write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[d9]"></a>GPIO_PinLockConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[da]"></a>GPIO_EventOutputConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[db]"></a>GPIO_EventOutputCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>GPIO_PinRemapConfig</STRONG> (Thumb, 138 bytes, Stack size 20 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[dd]"></a>GPIO_EXTILineConfig</STRONG> (Thumb, 66 bytes, Stack size 12 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>GPIO_ETH_MediaInterfaceConfig</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_gpio.o(.text), UNUSED)

<P><STRONG><a name="[df]"></a>RCC_DeInit</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e0]"></a>RCC_HSEConfig</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[65]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f10x_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
</UL>

<P><STRONG><a name="[64]"></a>RCC_WaitForHSEStartUp</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f10x_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
</UL>

<P><STRONG><a name="[e1]"></a>RCC_AdjustHSICalibrationValue</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e2]"></a>RCC_HSICmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e3]"></a>RCC_PLLConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e4]"></a>RCC_PLLCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e5]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e6]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e7]"></a>RCC_HCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e8]"></a>RCC_PCLK1Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[e9]"></a>RCC_PCLK2Config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[ea]"></a>RCC_ITConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[eb]"></a>RCC_USBCLKConfig</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[ec]"></a>RCC_ADCCLKConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[ed]"></a>RCC_LSEConfig</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[ee]"></a>RCC_LSICmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[ef]"></a>RCC_RTCCLKConfig</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[f0]"></a>RCC_RTCCLKCmd</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[69]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 192 bytes, Stack size 12 bytes, stm32f10x_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[f1]"></a>RCC_AHBPeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[76]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Init
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[f2]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[62]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_AFIODeInit
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_DeInit
</UL>

<P><STRONG><a name="[67]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
</UL>

<P><STRONG><a name="[f3]"></a>RCC_BackupResetCmd</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[f4]"></a>RCC_ClockSecuritySystemCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[f5]"></a>RCC_MCOConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[f6]"></a>RCC_ClearFlag</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[f7]"></a>RCC_GetITStatus</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[f8]"></a>RCC_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_rcc.o(.text), UNUSED)

<P><STRONG><a name="[66]"></a>USART_DeInit</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, stm32f10x_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[68]"></a>USART_Init</STRONG> (Thumb, 210 bytes, Stack size 56 bytes, stm32f10x_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[f9]"></a>USART_StructInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[fa]"></a>USART_ClockInit</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[fb]"></a>USART_ClockStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[78]"></a>USART_ITConfig</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, stm32f10x_usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[fc]"></a>USART_DMACmd</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[fd]"></a>USART_SetAddress</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[fe]"></a>USART_WakeUpConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[ff]"></a>USART_ReceiverWakeUpCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[100]"></a>USART_LINBreakDetectLengthConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[101]"></a>USART_LINCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[102]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[103]"></a>USART_SendBreak</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[104]"></a>USART_SetGuardTime</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[105]"></a>USART_SetPrescaler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[106]"></a>USART_SmartCardCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[107]"></a>USART_SmartCardNACKCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[108]"></a>USART_HalfDuplexCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[109]"></a>USART_OverSampling8Cmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[10a]"></a>USART_OneBitMethodCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[10b]"></a>USART_IrDAConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[10c]"></a>USART_IrDACmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[10d]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[10e]"></a>USART_ClearFlag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f10x_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[10f]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32f10x_usart.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>delay_init</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, delay.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[110]"></a>delay_us</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, delay.o(.text), UNUSED)

<P><STRONG><a name="[7d]"></a>delay_ms</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, delay.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[111]"></a>STM32_Flash_Capacity</STRONG> (Thumb, 154 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[6a]"></a>STM32_CPUID</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[71]"></a>MY_NVIC_SetVectorTable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_RCC_DeInit
</UL>

<P><STRONG><a name="[6c]"></a>MY_NVIC_PriorityGroup_Config</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = MY_NVIC_PriorityGroup_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_Init
</UL>

<P><STRONG><a name="[6e]"></a>MY_NVIC_Init</STRONG> (Thumb, 42 bytes, Stack size 24 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_PriorityGroup_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USARTx_Init
</UL>

<P><STRONG><a name="[70]"></a>MY_RCC_DeInit</STRONG> (Thumb, 90 bytes, Stack size 4 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_SetVectorTable
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STM_Clock_Init
</UL>

<P><STRONG><a name="[72]"></a>SYS_Standby</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WFI_SET
</UL>

<P><STRONG><a name="[112]"></a>SYS_SoftReset</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[113]"></a>STM_JTAG_Set</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>STM_Clock_Init</STRONG> (Thumb, 134 bytes, Stack size 12 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_RCC_DeInit
</UL>

<P><STRONG><a name="[114]"></a>BCD_to_HEX</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[115]"></a>HEX_to_BCD</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[116]"></a>DX_to_HX</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[117]"></a>HX_to_DX</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, sys.o(.text), UNUSED)

<P><STRONG><a name="[5e]"></a>_sys_exit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[118]"></a>fputc</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, usart.o(.text), UNUSED)

<P><STRONG><a name="[75]"></a>USARTx_Init</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_Init
</UL>

<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 180 bytes, Stack size 8 bytes, usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>main</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = main &rArr; AD9959_Set_Fre &rArr; Write_CFTW0 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Phase
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Fre
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Amp
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Init
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MY_NVIC_PriorityGroup_Config
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[86]"></a>AD9959_WriteData</STRONG> (Thumb, 212 bytes, Stack size 20 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPSK
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetASK
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFSK
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Modulation_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Phase
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Ampli
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Fre
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_FDW
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_RDW
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_LSRR
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ACR
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Phase
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Fre
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Amp
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Init
</UL>

<P><STRONG><a name="[84]"></a>delay1</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ad9959.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntReset
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IO_Update
</UL>

<P><STRONG><a name="[83]"></a>IntReset</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = IntReset
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay1
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Init
</UL>

<P><STRONG><a name="[85]"></a>Intserve</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, ad9959.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Init
</UL>

<P><STRONG><a name="[7e]"></a>AD9959_Init</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AD9959_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Intserve
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IntReset
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>IO_Update</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = IO_Update
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay1
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[87]"></a>Write_CFTW0</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Write_CFTW0 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPSK
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetASK
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFSK
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Fre
</UL>

<P><STRONG><a name="[8b]"></a>Write_ACR</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Write_ACR &rArr; AD9959_WriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetASK
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Amp
</UL>

<P><STRONG><a name="[8c]"></a>Write_CPOW0</STRONG> (Thumb, 32 bytes, Stack size 12 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Write_CPOW0 &rArr; AD9959_WriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPSK
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetASK
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFSK
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_Set_Phase
</UL>

<P><STRONG><a name="[8d]"></a>Write_LSRR</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
</UL>

<P><STRONG><a name="[8e]"></a>Write_RDW</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
</UL>

<P><STRONG><a name="[8f]"></a>Write_FDW</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
</UL>

<P><STRONG><a name="[90]"></a>Write_Profile_Fre</STRONG> (Thumb, 84 bytes, Stack size 40 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFSK
</UL>

<P><STRONG><a name="[91]"></a>Write_Profile_Ampli</STRONG> (Thumb, 44 bytes, Stack size 20 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetAmp_Sweep
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetASK
</UL>

<P><STRONG><a name="[92]"></a>Write_Profile_Phase</STRONG> (Thumb, 44 bytes, Stack size 20 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPhase_Sweep
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetPSK
</UL>

<P><STRONG><a name="[7f]"></a>AD9959_Set_Fre</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AD9959_Set_Fre &rArr; Write_CFTW0 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>AD9959_Set_Amp</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = AD9959_Set_Amp &rArr; Write_ACR &rArr; AD9959_WriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ACR
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[81]"></a>AD9959_Set_Phase</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ad9959.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AD9959_Set_Phase &rArr; Write_CPOW0 &rArr; AD9959_WriteData
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[93]"></a>AD9959_Modulation_Init</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[94]"></a>AD9959_SetFSK</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Fre
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[95]"></a>AD9959_SetASK</STRONG> (Thumb, 74 bytes, Stack size 32 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Ampli
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ACR
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[96]"></a>AD9959_SetPSK</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Phase
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[97]"></a>AD9959_SetFre_Sweep</STRONG> (Thumb, 130 bytes, Stack size 48 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Fre
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_FDW
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_RDW
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_LSRR
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ACR
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[98]"></a>AD9959_SetAmp_Sweep</STRONG> (Thumb, 104 bytes, Stack size 48 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Ampli
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_FDW
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_RDW
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_LSRR
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[99]"></a>AD9959_SetPhase_Sweep</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, ad9959.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Phase
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_FDW
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_RDW
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_LSRR
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CPOW0
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ACR
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_WriteData
</UL>

<P><STRONG><a name="[119]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[6b]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STM32_CPUID
</UL>

<P><STRONG><a name="[9f]"></a>_printf_pre_padding</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[a0]"></a>_printf_post_padding</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, _printf_pad.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_post_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>

<P><STRONG><a name="[53]"></a>_printf_int_hex</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, _printf_hex_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _printf_int_hex &rArr; _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_x
</UL>

<P><STRONG><a name="[11a]"></a>_printf_longlong_hex</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, _printf_hex_int.o(.text), UNUSED)

<P><STRONG><a name="[9c]"></a>__printf</STRONG> (Thumb, 308 bytes, Stack size 40 bytes, __printf_flags_wp.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[11b]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[11c]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[11d]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[11e]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[11f]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _printf_int_common &rArr; _printf_post_padding
</UL>
<BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_hex
</UL>

<P><STRONG><a name="[9a]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[4b]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[55]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[5a]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[120]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[121]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[9d]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[8a]"></a>__aeabi_d2uiz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Fre
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
</UL>

<P><STRONG><a name="[a4]"></a>_dfixu</STRONG> (Thumb, 90 bytes, Stack size 32 bytes, dfixu.o(x$fpl$dfixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[88]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Fre
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
</UL>

<P><STRONG><a name="[122]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[89]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD9959_SetFre_Sweep
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Profile_Fre
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_CFTW0
</UL>

<P><STRONG><a name="[a6]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[a5]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfixu
</UL>

<P><STRONG><a name="[a7]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[60]"></a>SetSysClockTo72</STRONG> (Thumb, 214 bytes, Stack size 12 bytes, system_stm32f10x.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>

<P><STRONG><a name="[5f]"></a>SetSysClock</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_stm32f10x.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[4c]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
