# STM32F407ZGT6 AD9959驱动移植说明

## 概述
本项目将原本基于STM32F103的AD9959驱动程序移植到STM32F407ZGT6平台。AD9959是一款四通道直接数字频率合成器(DDS)，支持正弦波点频、控幅、控相输出。

## 主要移植变化

### 1. 头文件更新
- `stm32f10x.h` → `stm32f4xx.h`
- 更新了所有相关的头文件引用

### 2. GPIO配置更新
STM32F4系列的GPIO配置方式与F1系列不同，主要变化：

**F1系列配置方式：**
```c
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
```

**F4系列配置方式：**
```c
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
```

### 3. 时钟配置更新
- F1系列使用APB2时钟：`RCC_APB2PeriphClockCmd()`
- F4系列使用AHB1时钟：`RCC_AHB1PeriphClockCmd()`

### 4. 系统时钟频率调整
- F103默认时钟：72MHz → F407默认时钟：168MHz
- 更新了`delay_init(168)`参数

## 硬件连接保持不变

AD9959与STM32F407ZGT6的连接方式：
```
AD9959引脚    STM32F407引脚    功能
CS           PA6             片选信号
SCLK         PB1             串行时钟
UPDATE       PB0             更新信号
PS0          PA7             Profile选择0
PS1          PA2             Profile选择1
PS2          PB10            Profile选择2
PS3          PC0             Profile选择3
SDIO0        PA5             串行数据0
SDIO1        PA4             串行数据1
SDIO2        PA3             串行数据2
SDIO3        PA8             串行数据3
AD9959_PWR   PA9             电源控制
Reset        PA10            复位信号
```

## 功能特性

### 基本功能
- 四通道独立频率控制（0-200MHz）
- 幅度控制（0-1023，对应0-500mVpp）
- 相位控制（0-16383，对应0°-360°）

### 调制功能
- FSK（频移键控）
- ASK（幅移键控）
- PSK（相移键控）
- 频率/幅度/相位扫描

## 使用方法

### 初始化
```c
AD9959_Init();  // 初始化AD9959
```

### 设置频率
```c
AD9959_Set_Fre(CH0, 100000);  // 设置通道0频率为100kHz
```

### 设置幅度
```c
AD9959_Set_Amp(CH0, 1023);    // 设置通道0幅度为最大值
```

### 设置相位
```c
AD9959_Set_Phase(CH0, 0);     // 设置通道0相位为0°
```

### 更新输出
```c
IO_Update();  // 使设置生效
```

## 编译环境要求
- Keil MDK-ARM 5.x或更高版本
- STM32F4xx标准外设库
- 支持STM32F407ZGT6的开发板

## 注意事项
1. 确保使用正确的STM32F4xx标准外设库
2. 检查时钟配置是否正确
3. 验证GPIO引脚连接
4. AD9959外部晶振频率为25MHz

## 测试验证
移植完成后，建议进行以下测试：
1. 基本初始化测试
2. 单通道频率输出测试
3. 多通道同时输出测试
4. 幅度和相位控制测试

## 版本信息
- 原版本：基于STM32F103RCT6
- 移植版本：基于STM32F407ZGT6
- 移植日期：2025年
- 功能保持：完全兼容原有功能
