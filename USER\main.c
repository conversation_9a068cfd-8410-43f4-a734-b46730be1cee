/**********************************************************
                       康威电子
功能：STM32F407ZGT6驱动AD9959模块，正弦点频，控幅控相输出代码
接口：具体接口请参考AD9959.h
时间：2025年
版本：3.0 (移植到STM32F407ZGT6)
作者：移植版本
声明：本程序仅供学习使用，不得用于商业用途

移植说明：
- 原版本基于STM32F103RCT6
- 现版本移植到STM32F407ZGT6
- 保持所有原有功能不变
- 更新了GPIO配置和时钟设置
**********************************************************/

#include "stm32_config.h"
#include "stdio.h"
#include "AD9959.h"

int main(void)
{
	MY_NVIC_PriorityGroup_Config(NVIC_PriorityGroup_2);	//�����жϷ���
	// 根据不同STM32系列配置延时初始化
	#ifdef STM32F4_SERIES
		delay_init(168);	//STM32F4系列延时函数初始化，168MHz
	#else
		delay_init(72);		//STM32F1系列延时函数初始化，72MHz
	#endif
	delay_ms(500);//��ʱһ������ȴ��ϵ��ȶ�,ȷ��AD9959�ȿ��ư����ϵ硣
	
	//移植说明：
	//1.已将STM32F103的代码移植到STM32F407ZGT6
	//2.更新了GPIO配置方式，适配STM32F4系列
	//3.更新了时钟配置，从72MHz改为168MHz
	//4.硬件连接保持不变，功能完全兼容
	//5.如需修改引脚定义，请参考AD9959.h文件
	
	AD9959_Init();								//��ʼ������AD9959��Ҫ�õ���IO��,���Ĵ���
	AD9959_Set_Fre(CH0, 100000);	//����ͨ��0Ƶ��100000Hz
	AD9959_Set_Fre(CH1, 100000);	//����ͨ��1Ƶ��100000Hz
	AD9959_Set_Fre(CH2, 100000);	//����ͨ��2Ƶ��100000Hz
	AD9959_Set_Fre(CH3, 100000);	//����ͨ��3Ƶ��100000Hz
		
	AD9959_Set_Amp(CH0, 1023); 		//����ͨ��0���ȿ���ֵ1023����Χ0~1023
	AD9959_Set_Amp(CH1, 1023); 		//����ͨ��1���ȿ���ֵ1023����Χ0~1023
	AD9959_Set_Amp(CH2, 1023); 		//����ͨ��2���ȿ���ֵ1023����Χ0~1023
	AD9959_Set_Amp(CH3, 1023); 		//����ͨ��3���ȿ���ֵ1023����Χ0~1023

	AD9959_Set_Phase(CH0, 0);			//����ͨ��0��λ����ֵ0(0��)����Χ0~16383
	AD9959_Set_Phase(CH1, 4096);	//����ͨ��1��λ����ֵ4096(90��)����Χ0~16383
	AD9959_Set_Phase(CH2, 8192);	//����ͨ��2��λ����ֵ8192(180��)����Χ0~16383
	AD9959_Set_Phase(CH3, 12288);	//����ͨ��3��λ����ֵ12288(270��)����Χ0~16383
	IO_Update();	//AD9959��������,���ô˺���������������Ч��������
	while(1);
}


