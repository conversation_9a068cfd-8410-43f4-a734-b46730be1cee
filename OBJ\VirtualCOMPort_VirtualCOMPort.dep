Dependencies for Project 'VirtualCOMPort', Target 'VirtualCOMPort': (DO NOT MODIFY !)
CompilerVersion: 5060750::V5.06 update 6 (build 750)::.\ARMCC5
F (..\CMSIS\CM3\CORE\core_cm3.c)(0x56376F42)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\core_cm3.o --omf_browse ..\obj\core_cm3.crf --depend ..\obj\core_cm3.d)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC5\include\stdint.h)(0x5D9B3488)
F (..\CMSIS\CM3\stm32f10x_it.c)(0x646ECABE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_it.o --omf_browse ..\obj\stm32f10x_it.crf --depend ..\obj\stm32f10x_it.d)
I (..\CMSIS\CM3\stm32f10x_it.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\CM3\system_stm32f10x.c)(0x56376F42)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\system_stm32f10x.o --omf_browse ..\obj\system_stm32f10x.crf --depend ..\obj\system_stm32f10x.d)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\CM3\STARTUP\startup_stm32f10x_hd.s)(0x56376F42)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

--pd "__UVISION_VERSION SETA 541" --pd "STM32F407xx SETA 1"

--list ..\lis\startup_stm32f10x_hd.lst --xref -o ..\obj\startup_stm32f10x_hd.o --depend ..\obj\startup_stm32f10x_hd.d)
F (..\CMSIS\STM32LIB\src\misc.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\misc.o --omf_browse ..\obj\misc.crf --depend ..\obj\misc.d)
I (..\CMSIS\STM32LIB\inc\misc.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\CM3\stm32f4xx_gpio.c)(0x6889B5B9)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f4xx_gpio.o --omf_browse ..\obj\stm32f4xx_gpio.crf --depend ..\obj\stm32f4xx_gpio.d)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_exti.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_exti.o --omf_browse ..\obj\stm32f10x_exti.crf --depend ..\obj\stm32f10x_exti.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_exti.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_fsmc.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_fsmc.o --omf_browse ..\obj\stm32f10x_fsmc.crf --depend ..\obj\stm32f10x_fsmc.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_fsmc.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_gpio.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_gpio.o --omf_browse ..\obj\stm32f10x_gpio.crf --depend ..\obj\stm32f10x_gpio.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_gpio.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_rcc.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_rcc.o --omf_browse ..\obj\stm32f10x_rcc.crf --depend ..\obj\stm32f10x_rcc.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_rcc.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_usart.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_usart.o --omf_browse ..\obj\stm32f10x_usart.crf --depend ..\obj\stm32f10x_usart.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_usart.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_adc.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_adc.o --omf_browse ..\obj\stm32f10x_adc.crf --depend ..\obj\stm32f10x_adc.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_adc.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_bkp.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_bkp.o --omf_browse ..\obj\stm32f10x_bkp.crf --depend ..\obj\stm32f10x_bkp.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_bkp.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_can.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_can.o --omf_browse ..\obj\stm32f10x_can.crf --depend ..\obj\stm32f10x_can.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_can.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_cec.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_cec.o --omf_browse ..\obj\stm32f10x_cec.crf --depend ..\obj\stm32f10x_cec.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_cec.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_crc.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_crc.o --omf_browse ..\obj\stm32f10x_crc.crf --depend ..\obj\stm32f10x_crc.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_crc.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_dac.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_dac.o --omf_browse ..\obj\stm32f10x_dac.crf --depend ..\obj\stm32f10x_dac.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_dac.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_dbgmcu.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_dbgmcu.o --omf_browse ..\obj\stm32f10x_dbgmcu.crf --depend ..\obj\stm32f10x_dbgmcu.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_dbgmcu.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_dma.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_dma.o --omf_browse ..\obj\stm32f10x_dma.crf --depend ..\obj\stm32f10x_dma.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_dma.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_flash.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_flash.o --omf_browse ..\obj\stm32f10x_flash.crf --depend ..\obj\stm32f10x_flash.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_flash.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_i2c.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_i2c.o --omf_browse ..\obj\stm32f10x_i2c.crf --depend ..\obj\stm32f10x_i2c.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_i2c.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_iwdg.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_iwdg.o --omf_browse ..\obj\stm32f10x_iwdg.crf --depend ..\obj\stm32f10x_iwdg.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_iwdg.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_pwr.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_pwr.o --omf_browse ..\obj\stm32f10x_pwr.crf --depend ..\obj\stm32f10x_pwr.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_pwr.h)(0x56376F41)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_rtc.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_rtc.o --omf_browse ..\obj\stm32f10x_rtc.crf --depend ..\obj\stm32f10x_rtc.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_rtc.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_sdio.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_sdio.o --omf_browse ..\obj\stm32f10x_sdio.crf --depend ..\obj\stm32f10x_sdio.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_sdio.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_spi.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_spi.o --omf_browse ..\obj\stm32f10x_spi.crf --depend ..\obj\stm32f10x_spi.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_spi.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_tim.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_tim.o --omf_browse ..\obj\stm32f10x_tim.crf --depend ..\obj\stm32f10x_tim.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_tim.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\CMSIS\STM32LIB\src\stm32f10x_wwdg.c)(0x56376F41)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\stm32f10x_wwdg.o --omf_browse ..\obj\stm32f10x_wwdg.crf --depend ..\obj\stm32f10x_wwdg.d)
I (..\CMSIS\STM32LIB\inc\stm32f10x_wwdg.h)(0x56376F42)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\SYSTEM\delay\delay.c)(0x56376F3F)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\delay.o --omf_browse ..\obj\delay.crf --depend ..\obj\delay.d)
I (..\SYSTEM\delay\delay.h)(0x56376F3F)
I (..\SYSTEM\sys\sys.h)(0x6889B04B)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\SYSTEM\sys\sys.c)(0x56376F3F)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\sys.o --omf_browse ..\obj\sys.crf --depend ..\obj\sys.d)
I (..\SYSTEM\sys\sys.h)(0x6889B04B)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\SYSTEM\usart\usart.c)(0x56376F3F)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\usart.o --omf_browse ..\obj\usart.crf --depend ..\obj\usart.d)
I (..\SYSTEM\usart\usart.h)(0x56376F3F)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCC5\include\stdio.h)(0x5D9B348A)
I (..\SYSTEM\sys\sys.h)(0x6889B04B)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (.\main.c)(0x6889B0A4)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\main.o --omf_browse ..\obj\main.crf --depend ..\obj\main.d)
I (..\HARDWARE\stm32_config.h)(0x6889B064)
I (..\SYSTEM\sys\sys.h)(0x6889B04B)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
F (..\HARDWARE\AD9959\AD9959.c)(0x6889B5DE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork -I ..\CMSIS\CM3 -I ..\CMSIS\CM3\CORE -I ..\CMSIS\CM3\STARTUP -I ..\CMSIS\STM32LIB\inc -I ..\SYSTEM\delay -I ..\SYSTEM\sys -I ..\SYSTEM\usart -I ..\USER -I ..\USB\USBLIB\inc -I ..\USB\Mass_Storage\inc -I ..\HARDWARE -I ..\Com-Rule -I ..\HARDWARE\AD9959

-IC:\Users\<USER>\AppData\Local\Arm\Packs\Keil\STM32F4xx_DFP\3.0.0\Device\Include

-D__UVISION_VERSION="541" -DSTM32F407xx -DSTM32F40_41xxx -DUSE_STDPERIPH_DRIVER

-o ..\obj\ad9959.o --omf_browse ..\obj\ad9959.crf --depend ..\obj\ad9959.d)
I (..\HARDWARE\AD9959\AD9959.h)(0x647D486F)
I (..\SYSTEM\sys\sys.h)(0x6889B04B)
I (..\CMSIS\CM3\stm32f10x.h)(0x56376F42)
