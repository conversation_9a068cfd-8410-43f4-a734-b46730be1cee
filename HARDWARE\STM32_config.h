/**********************************************************
* @ File name -> config.h
* @ Version   -> V1.0
* @ Date      -> 11-04-2013
* @ Brief     -> ϵͳ����ͷ�ļ�
* @           -> ���ӻ��߼�����ص�ͷ�ļ������ڴ�ͷ�ļ�����
**********************************************************/

#ifndef		_STM32_config_h_
#define		_STM32_config_h_

/**********************************************************
                     �ⲿ����ͷ�ļ�
         Ӧ�õ���ͬ������ͷ�ļ����������޸ļ���                        
**********************************************************/

#include "sys.h"
// 条件编译支持STM32F1和STM32F4系列
// 优先检查STM32F1定义，因为我们使用STM32F1的库文件
#if defined(STM32F10X_LD) || defined(STM32F10X_LD_VL) || defined(STM32F10X_MD) || defined(STM32F10X_MD_VL) || defined(STM32F10X_HD) || defined(STM32F10X_HD_VL) || defined(STM32F10X_XL) || defined(STM32F10X_CL)
    #include "stm32f10x.h"
    #define STM32F1_SERIES
    // 如果同时定义了STM32F4，则启用STM32F4特性
    #if defined(STM32F40_41xxx) || defined(STM32F427_437xx) || defined(STM32F429_439xx) || defined(STM32F401xx) || defined(STM32F411xE) || defined(STM32F446xx)
        #define STM32F4_SERIES
        // 包含STM32F4兼容头文件
        #include "stm32f4xx.h"
    #endif
#else
    // 默认使用STM32F1系列
    #include "stm32f10x.h"
    #define STM32F1_SERIES
#endif

#include "string.h"	//�ڴ������غ�����
#include "math.h"	//��ѧ������غ�����

#include "delay.h"
#include "usart.h"




#endif

