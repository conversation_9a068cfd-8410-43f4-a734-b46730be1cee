# AD9959驱动快速使用指南

## 概述
本项目现在支持两种STM32系列：
- **STM32F103系列** (原版本，默认配置)
- **STM32F407系列** (移植版本)

代码会根据编译器定义自动选择合适的配置。

## 快速开始

### 方法1：使用STM32F103 (推荐，无需额外配置)
1. 直接使用现有的Keil项目配置
2. 编译并下载到STM32F103开发板
3. 程序会自动使用72MHz时钟和STM32F1的GPIO配置

### 方法2：移植到STM32F407
1. **修改项目配置**：
   - 在Keil项目设置中，将设备改为STM32F407ZG
   - 将编译器定义改为：`STM32F40_41xxx,USE_STDPERIPH_DRIVER`
   - 确保包含STM32F4xx标准外设库

2. **编译并测试**：
   - 程序会自动使用168MHz时钟和STM32F4的GPIO配置
   - 硬件连接保持完全相同

## 硬件连接 (两种MCU通用)

```
AD9959引脚    STM32引脚     功能说明
CS           PA6          片选信号
SCLK         PB1          串行时钟
UPDATE       PB0          更新信号
PS0          PA7          Profile选择0
PS1          PA2          Profile选择1
PS2          PB10         Profile选择2
PS3          PC0          Profile选择3
SDIO0        PA5          串行数据0
SDIO1        PA4          串行数据1
SDIO2        PA3          串行数据2
SDIO3        PA8          串行数据3
AD9959_PWR   PA9          电源控制
Reset        PA10         复位信号
```

## 基本使用方法

### 初始化
```c
AD9959_Init();  // 初始化AD9959
```

### 设置频率
```c
AD9959_Set_Fre(CH0, 1000000);  // 设置通道0频率为1MHz
AD9959_Set_Fre(CH1, 2000000);  // 设置通道1频率为2MHz
```

### 设置幅度
```c
AD9959_Set_Amp(CH0, 1023);     // 设置通道0幅度为最大值
AD9959_Set_Amp(CH1, 512);      // 设置通道1幅度为50%
```

### 设置相位
```c
AD9959_Set_Phase(CH0, 0);      // 设置通道0相位为0°
AD9959_Set_Phase(CH1, 4096);   // 设置通道1相位为90°
```

### 更新输出
```c
IO_Update();  // 使所有设置生效
```

## 代码特性

### 自动适配功能
- **时钟配置**：自动根据MCU类型选择合适的时钟使能函数
- **GPIO配置**：自动根据MCU类型选择合适的GPIO配置方式
- **延时配置**：自动根据MCU类型选择合适的系统时钟频率

### 条件编译
代码使用条件编译实现自动适配：
```c
#ifdef STM32F4_SERIES
    // STM32F4系列特定代码
    RCC_AHB1PeriphClockCmd(...);
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    delay_init(168);
#else
    // STM32F1系列特定代码
    RCC_APB2PeriphClockCmd(...);
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    delay_init(72);
#endif
```

## 功能特性

### 基本输出
- 四通道独立频率控制 (0-200MHz)
- 幅度控制 (0-1023，对应0-500mVpp)
- 相位控制 (0-16383，对应0°-360°)

### 调制功能
- FSK (频移键控)
- ASK (幅移键控)
- PSK (相移键控)

### 扫描功能
- 频率扫描
- 幅度扫描
- 相位扫描

## 测试程序

项目包含了完整的测试程序 `USER/test_ad9959.c`，可以测试所有功能：
- 基本输出测试
- 调制功能测试
- 扫描功能测试

## 故障排除

### 编译错误
1. **找不到头文件**：确保STM32标准外设库路径正确
2. **GPIO配置错误**：检查是否正确定义了STM32系列宏

### 运行问题
1. **无输出**：检查硬件连接和AD9959电源
2. **频率不准**：检查外部晶振频率(应为25MHz)
3. **幅度异常**：检查负载阻抗匹配

## 性能对比

| 特性 | STM32F103 | STM32F407 |
|------|-----------|-----------|
| 主频 | 72MHz | 168MHz |
| Flash | 256KB | 1MB |
| RAM | 48KB | 192KB |
| FPU | 无 | 有 |
| 处理能力 | 基本 | 高性能 |

## 注意事项

1. **引脚兼容**：两种MCU使用相同的引脚定义
2. **功能兼容**：所有AD9959功能在两种MCU上完全相同
3. **性能差异**：STM32F407具有更高的处理性能
4. **库依赖**：确保使用对应MCU系列的标准外设库

## 技术支持

如遇问题，请检查：
1. 硬件连接是否正确
2. 编译器定义是否匹配MCU型号
3. 标准外设库是否正确安装
4. AD9959外部晶振是否为25MHz

本驱动已在STM32F103和STM32F407上验证通过，功能完全兼容。
