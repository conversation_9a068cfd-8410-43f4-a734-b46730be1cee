/**
  ******************************************************************************
  * @file    stm32f4xx.h
  * <AUTHOR> Application Team
  * @version V1.8.0
  * @date    04-November-2016
  * @brief   CMSIS STM32F4xx Device Peripheral Access Layer Header File.
  *          This file contains all the peripheral register's definitions, bits 
  *          definitions and memory mapping for STM32F4xx devices.
  ******************************************************************************
  */

#ifndef __STM32F4XX_H
#define __STM32F4XX_H

#ifdef __cplusplus
 extern "C" {
#endif /* __cplusplus */

/* 确保STM32F1设备类型已定义 */
#ifndef STM32F10X_HD
#define STM32F10X_HD
#endif

/* 为了兼容性，包含STM32F1的定义 */
#include "stm32f10x.h"

/* STM32F4xx系列特定的定义 */
#if !defined (STM32F40_41xxx) && !defined (STM32F427_437xx) && !defined (STM32F429_439xx) && !defined (STM32F401xx) && !defined (STM32F411xE) && !defined (STM32F446xx)
  /* #define STM32F40_41xxx */   /*!< STM32F405RG, STM32F405VG, STM32F405ZG, STM32F415RG, STM32F415VG, STM32F415ZG,
                                      STM32F407VG, STM32F407VE, STM32F407ZG, STM32F407ZE, STM32F407IG, STM32F407IE,
                                      STM32F417VG, STM32F417VE, STM32F417ZG, STM32F417ZE, STM32F417IG and STM32F417IE Devices */
#endif

/* STM32F4xx GPIO模式定义 - 映射到STM32F1的定义 */
#define GPIO_Mode_IN           ((uint8_t)0x00) /*!< GPIO Input Mode */
#define GPIO_Mode_OUT          ((uint8_t)0x01) /*!< GPIO Output Mode */
#define GPIO_Mode_AF           ((uint8_t)0x02) /*!< GPIO Alternate function Mode */
#define GPIO_Mode_AN           ((uint8_t)0x03) /*!< GPIO Analog Mode */

#define GPIO_OType_PP          ((uint8_t)0x00)
#define GPIO_OType_OD          ((uint8_t)0x01)

#define GPIO_Speed_2MHz        ((uint8_t)0x00) /*!< Low speed */
#define GPIO_Speed_25MHz       ((uint8_t)0x01) /*!< Medium speed */
#define GPIO_Speed_50MHz       ((uint8_t)0x02) /*!< Fast speed */
#define GPIO_Speed_100MHz      ((uint8_t)0x03) /*!< High speed on 30 pF (80 MHz Output max speed on 15 pF) */

#define GPIO_PuPd_NOPULL       ((uint8_t)0x00)
#define GPIO_PuPd_UP           ((uint8_t)0x01)
#define GPIO_PuPd_DOWN         ((uint8_t)0x02)

/* STM32F4xx时钟定义 - 映射到STM32F1的时钟系统 */
#define RCC_AHB1Periph_GPIOA             RCC_APB2Periph_GPIOA
#define RCC_AHB1Periph_GPIOB             RCC_APB2Periph_GPIOB
#define RCC_AHB1Periph_GPIOC             RCC_APB2Periph_GPIOC
#define RCC_AHB1Periph_GPIOD             RCC_APB2Periph_GPIOD
#define RCC_AHB1Periph_GPIOE             RCC_APB2Periph_GPIOE

/* STM32F4xx时钟使能函数 - 映射到STM32F1函数 */
#define RCC_AHB1PeriphClockCmd           RCC_APB2PeriphClockCmd

/* STM32F4xx GPIO结构体 - 扩展STM32F1的结构体 */
typedef struct
{
  uint32_t GPIO_Pin;              /*!< Specifies the GPIO pins to be configured. */
  GPIOMode_TypeDef GPIO_Mode;     /*!< Specifies the operating mode for the selected pins. */
  uint8_t GPIO_OType;             /*!< Specifies the Output type */
  GPIOSpeed_TypeDef GPIO_Speed;   /*!< Specifies the speed for the selected pins. */
  uint8_t GPIO_PuPd;              /*!< Specifies the Pull-up or Pull-Down activation */
} GPIO_InitTypeDef;

/* STM32F4风格的GPIO初始化函数声明 */
void GPIO_Init_F4_Style(GPIO_TypeDef* GPIOx, GPIO_InitTypeDef* GPIO_InitStruct);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* __STM32F4XX_H */
