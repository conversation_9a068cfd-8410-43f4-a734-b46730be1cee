Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_adc.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_BackupResetCmd
    stm32f10x_can.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_cec.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f10x_spi.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_wwdg.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphResetCmd
    delay.o(.text) refers to delay.o(.data) for fac_us
    sys.o(.text) refers to _printf_pad.o(.text) for _printf_pre_padding
    sys.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    sys.o(.text) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    sys.o(.text) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    sys.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    sys.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    sys.o(.text) refers to sys.o(.emb_text) for WFI_SET
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to sys.o(.text) for MY_NVIC_Init
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_DeInit
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    main.o(.text) refers to sys.o(.text) for MY_NVIC_PriorityGroup_Config
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to ad9959.o(.text) for AD9959_Init
    ad9959.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    ad9959.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    ad9959.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(.text) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(.text) refers to ad9959.o(.data) for FR1_DATA
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing stm32f10x_exti.o(.text), (284 bytes).
    Removing stm32f10x_fsmc.o(.text), (1548 bytes).
    Removing stm32f10x_adc.o(.text), (1102 bytes).
    Removing stm32f10x_bkp.o(.text), (196 bytes).
    Removing stm32f10x_can.o(.text), (2544 bytes).
    Removing stm32f10x_cec.o(.text), (288 bytes).
    Removing stm32f10x_crc.o(.text), (72 bytes).
    Removing stm32f10x_dac.o(.text), (396 bytes).
    Removing stm32f10x_dbgmcu.o(.text), (48 bytes).
    Removing stm32f10x_dma.o(.text), (596 bytes).
    Removing stm32f10x_flash.o(.text), (1468 bytes).
    Removing stm32f10x_i2c.o(.text), (1028 bytes).
    Removing stm32f10x_iwdg.o(.text), (64 bytes).
    Removing stm32f10x_pwr.o(.text), (204 bytes).
    Removing stm32f10x_rtc.o(.text), (328 bytes).
    Removing stm32f10x_sdio.o(.text), (468 bytes).
    Removing stm32f10x_spi.o(.text), (780 bytes).
    Removing stm32f10x_tim.o(.text), (3610 bytes).
    Removing stm32f10x_wwdg.o(.text), (136 bytes).

20 unused section(s) (total 15192 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ..\CMSIS\CM3\CORE\core_cm3.c             0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CMSIS\CM3\STARTUP\startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\CMSIS\CM3\stm32f10x_it.c              0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    ..\CMSIS\CM3\system_stm32f10x.c          0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\misc.c             0x00000000   Number         0  misc.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_adc.c    0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_bkp.c    0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_can.c    0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_cec.c    0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_crc.c    0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_dac.c    0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_dbgmcu.c 0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_dma.c    0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_exti.c   0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_flash.c  0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_fsmc.c   0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_gpio.c   0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_i2c.c    0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_iwdg.c   0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_pwr.c    0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_rcc.c    0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_rtc.c    0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_sdio.c   0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_spi.c    0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_tim.c    0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_usart.c  0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\CMSIS\STM32LIB\src\stm32f10x_wwdg.c   0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    ..\HARDWARE\AD9959\AD9959.c              0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CMSIS\\CM3\\CORE\\core_cm3.c         0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001a4   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000017  0x080001aa   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001ae   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080001b0   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080001b2   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001b4   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001b4   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001b6   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001b6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001b6   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001bc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001bc   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001c0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001c0   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001c8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001ca   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001ca   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001ce   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x080001d4   Section        2  sys.o(.emb_text)
    .text                                    0x080001d6   Section        0  stm32f10x_it.o(.text)
    .text                                    0x080001f0   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x080001f1   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080002c7   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080003d0   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000410   Section        0  misc.o(.text)
    .text                                    0x080004ec   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08000848   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x08000bec   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x08000ff4   Section        0  delay.o(.text)
    .text                                    0x080010c4   Section        0  sys.o(.text)
    .text                                    0x080013e0   Section        0  usart.o(.text)
    .text                                    0x0800156c   Section        0  main.o(.text)
    .text                                    0x08001600   Section        0  ad9959.o(.text)
    .text                                    0x08001cb8   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08001cbc   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08001ce4   Section        0  _printf_pad.o(.text)
    .text                                    0x08001d34   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08001d8c   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x08001ec4   Section        0  heapauxi.o(.text)
    .text                                    0x08001eca   Section        2  use_no_semi.o(.text)
    .text                                    0x08001ecc   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08001f80   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001f81   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08001fb0   Section        0  _sputc.o(.text)
    .text                                    0x08001fba   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08002004   Section        0  exit.o(.text)
    .text                                    0x08002018   Section        8  libspace.o(.text)
    i._is_digit                              0x08002020   Section        0  __printf_wp.o(i._is_digit)
    x$fpl$dfixu                              0x08002030   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x0800208a   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x080020b0   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08002204   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080022a0   Section       12  dretinf.o(x$fpl$dretinf)
    .constdata                               0x080022ac   Section       40  _printf_hex_int.o(.constdata)
    x$fpl$usenofp                            0x080022ac   Section        0  usenofp.o(x$fpl$usenofp)
    uc_hextab                                0x080022ac   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x080022c0   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x080022d4   Section       17  __printf_flags_wp.o(.constdata)
    maptable                                 0x080022d4   Data          17  __printf_flags_wp.o(.constdata)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000024   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000028   Section        4  delay.o(.data)
    fac_us                                   0x20000028   Data           1  delay.o(.data)
    fac_ms                                   0x2000002a   Data           2  delay.o(.data)
    .data                                    0x2000002c   Section        6  usart.o(.data)
    .data                                    0x20000038   Section       19  ad9959.o(.data)
    .bss                                     0x2000004c   Section       63  usart.o(.bss)
    .bss                                     0x2000008c   Section       96  libspace.o(.bss)
    HEAP                                     0x200000f0   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200000f0   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x200002f0   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x200002f0   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200006f0   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_x                                0x080001a5   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_percent_end                      0x080001ab   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001af   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x080001b3   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001b5   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001b7   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001b7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001b7   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001c9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001cb   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001cf   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    WFI_SET                                  0x080001d5   Thumb Code     2  sys.o(.emb_text)
    NMI_Handler                              0x080001d7   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x080001d9   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x080001dd   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x080001e1   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x080001e5   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x080001e9   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x080001eb   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x080001ed   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x080001ef   Thumb Code     2  stm32f10x_it.o(.text)
    SystemInit                               0x080002cf   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x0800031d   Thumb Code   142  system_stm32f10x.o(.text)
    Reset_Handler                            0x080003d1   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x080003eb   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x080003ed   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    NVIC_PriorityGroupConfig                 0x08000411   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x0800041b   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x0800047f   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x0800048d   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x080004af   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x080004ed   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000599   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x080005ad   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x080006c3   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x080006d3   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x080006e5   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080006ed   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x080006ff   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08000707   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x0800070b   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x0800070f   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08000719   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x0800071d   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x0800072f   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08000749   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x0800074f   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x080007d9   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x0800081b   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08000849   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08000889   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x080008cf   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000907   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x0800093f   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08000953   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08000959   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08000971   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000977   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000989   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08000993   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x080009a5   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x080009b7   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x080009cb   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x080009e5   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x080009ed   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x080009ff   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08000a31   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000a37   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000a43   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000a4b   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x08000b0b   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000b25   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000b3f   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000b59   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000b73   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000b8d   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000b95   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x08000b9b   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08000ba1   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08000baf   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08000bc3   Thumb Code     6  stm32f10x_rcc.o(.text)
    USART_DeInit                             0x08000bed   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08000c73   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x08000d45   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x08000d5d   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x08000d7f   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x08000d8b   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x08000da3   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x08000ded   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x08000dff   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x08000e11   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08000e23   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08000e3b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x08000e4d   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08000e65   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x08000e6d   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x08000e77   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08000e81   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08000e91   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08000ea1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08000eb9   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08000ed1   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x08000ee9   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x08000eff   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x08000f17   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x08000f29   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x08000f41   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x08000f5b   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x08000f6d   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08000fc1   Thumb Code    52  stm32f10x_usart.o(.text)
    delay_init                               0x08000ff5   Thumb Code    56  delay.o(.text)
    delay_us                                 0x0800102d   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08001075   Thumb Code    72  delay.o(.text)
    STM32_Flash_Capacity                     0x080010c5   Thumb Code   154  sys.o(.text)
    STM32_CPUID                              0x0800115f   Thumb Code    20  sys.o(.text)
    MY_NVIC_SetVectorTable                   0x08001173   Thumb Code    14  sys.o(.text)
    MY_NVIC_PriorityGroup_Config             0x08001181   Thumb Code    12  sys.o(.text)
    MY_NVIC_Init                             0x0800118d   Thumb Code    42  sys.o(.text)
    MY_RCC_DeInit                            0x080011b7   Thumb Code    90  sys.o(.text)
    SYS_Standby                              0x08001211   Thumb Code    68  sys.o(.text)
    SYS_SoftReset                            0x08001255   Thumb Code    10  sys.o(.text)
    STM_JTAG_Set                             0x0800125f   Thumb Code    36  sys.o(.text)
    STM_Clock_Init                           0x08001283   Thumb Code   134  sys.o(.text)
    BCD_to_HEX                               0x08001309   Thumb Code    26  sys.o(.text)
    HEX_to_BCD                               0x08001323   Thumb Code    20  sys.o(.text)
    DX_to_HX                                 0x08001337   Thumb Code    70  sys.o(.text)
    HX_to_DX                                 0x0800137d   Thumb Code    48  sys.o(.text)
    _sys_exit                                0x080013e1   Thumb Code     6  usart.o(.text)
    fputc                                    0x080013e7   Thumb Code    24  usart.o(.text)
    USARTx_Init                              0x080013ff   Thumb Code   170  usart.o(.text)
    USART1_IRQHandler                        0x080014a9   Thumb Code   180  usart.o(.text)
    main                                     0x0800156d   Thumb Code   144  main.o(.text)
    AD9959_WriteData                         0x08001601   Thumb Code   212  ad9959.o(.text)
    delay1                                   0x080016d5   Thumb Code    18  ad9959.o(.text)
    IntReset                                 0x080016e7   Thumb Code    36  ad9959.o(.text)
    Intserve                                 0x0800170b   Thumb Code    70  ad9959.o(.text)
    AD9959_Init                              0x08001751   Thumb Code   122  ad9959.o(.text)
    IO_Update                                0x080017cb   Thumb Code    40  ad9959.o(.text)
    Write_CFTW0                              0x080017f3   Thumb Code    74  ad9959.o(.text)
    Write_ACR                                0x0800183d   Thumb Code    38  ad9959.o(.text)
    Write_CPOW0                              0x08001863   Thumb Code    32  ad9959.o(.text)
    Write_LSRR                               0x08001883   Thumb Code    30  ad9959.o(.text)
    Write_RDW                                0x080018a1   Thumb Code    44  ad9959.o(.text)
    Write_FDW                                0x080018cd   Thumb Code    44  ad9959.o(.text)
    Write_Profile_Fre                        0x080018f9   Thumb Code    84  ad9959.o(.text)
    Write_Profile_Ampli                      0x0800194d   Thumb Code    44  ad9959.o(.text)
    Write_Profile_Phase                      0x08001979   Thumb Code    44  ad9959.o(.text)
    AD9959_Set_Fre                           0x080019a5   Thumb Code    28  ad9959.o(.text)
    AD9959_Set_Amp                           0x080019c1   Thumb Code    28  ad9959.o(.text)
    AD9959_Set_Phase                         0x080019dd   Thumb Code    28  ad9959.o(.text)
    AD9959_Modulation_Init                   0x080019f9   Thumb Code   134  ad9959.o(.text)
    AD9959_SetFSK                            0x08001a7f   Thumb Code   110  ad9959.o(.text)
    AD9959_SetASK                            0x08001aed   Thumb Code    74  ad9959.o(.text)
    AD9959_SetPSK                            0x08001b37   Thumb Code    62  ad9959.o(.text)
    AD9959_SetFre_Sweep                      0x08001b75   Thumb Code   130  ad9959.o(.text)
    AD9959_SetAmp_Sweep                      0x08001bf7   Thumb Code   104  ad9959.o(.text)
    AD9959_SetPhase_Sweep                    0x08001c5f   Thumb Code    86  ad9959.o(.text)
    __use_no_semihosting                     0x08001cb9   Thumb Code     2  use_no_semi_2.o(.text)
    __2sprintf                               0x08001cbd   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x08001ce5   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08001d11   Thumb Code    34  _printf_pad.o(.text)
    _printf_int_hex                          0x08001d35   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08001d35   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08001d8d   Thumb Code   308  __printf_flags_wp.o(.text)
    __use_two_region_memory                  0x08001ec5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08001ec7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08001ec9   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08001ecb   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001ecb   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08001ecd   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x08001f8b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08001fb1   Thumb Code    10  _sputc.o(.text)
    __user_setup_stackheap                   0x08001fbb   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08002005   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08002019   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08002019   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08002019   Thumb Code     0  libspace.o(.text)
    _is_digit                                0x08002021   Thumb Code    14  __printf_wp.o(i._is_digit)
    __aeabi_d2uiz                            0x08002031   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08002031   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x0800208b   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x0800208b   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x080020b1   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080020b1   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08002205   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080022a1   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __I$use$fp                               0x080022ac   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x080022e8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002308   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x2000002c   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000030   Data           2  usart.o(.data)
    FR1_DATA                                 0x20000038   Data           3  ad9959.o(.data)
    FR2_DATA                                 0x2000003b   Data           2  ad9959.o(.data)
    ACC_FRE_FACTOR                           0x20000040   Data           8  ad9959.o(.data)
    CFR_DATA                                 0x20000048   Data           3  ad9959.o(.data)
    USART_RX_BUF                             0x2000004c   Data          63  usart.o(.bss)
    __libspace_start                         0x2000008c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000ec   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002354, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002308, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          139    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          581  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          762    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          764    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          766    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO          578    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO          577    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001aa   0x080001aa   0x00000004   Code   RO          604    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001ae   0x080001ae   0x00000002   Code   RO          636    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          643    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          645    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          648    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          650    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          652    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          655    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          657    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          659    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          661    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          663    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          665    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          667    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          669    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          671    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          673    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          675    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          679    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          681    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          683    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO          685    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000002   Code   RO          686    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080001b2   0x080001b2   0x00000002   Code   RO          704    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          714    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          716    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          719    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          722    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          724    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO          727    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001b4   0x080001b4   0x00000002   Code   RO          728    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO          597    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001b6   0x080001b6   0x00000000   Code   RO          611    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001b6   0x080001b6   0x00000006   Code   RO          623    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO          613    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO          614    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO          616    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001c0   0x080001c0   0x00000008   Code   RO          617    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001c8   0x080001c8   0x00000002   Code   RO          640    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO          688    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001ca   0x080001ca   0x00000004   Code   RO          689    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001ce   0x080001ce   0x00000006   Code   RO          690    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001d4   0x080001d4   0x00000002   Code   RO          445    .emb_text           sys.o
    0x080001d6   0x080001d6   0x0000001a   Code   RO           17    .text               stm32f10x_it.o
    0x080001f0   0x080001f0   0x000001e0   Code   RO          119    .text               system_stm32f10x.o
    0x080003d0   0x080003d0   0x00000040   Code   RO          140    .text               startup_stm32f10x_hd.o
    0x08000410   0x08000410   0x000000dc   Code   RO          144    .text               misc.o
    0x080004ec   0x080004ec   0x0000035c   Code   RO          183    .text               stm32f10x_gpio.o
    0x08000848   0x08000848   0x000003a4   Code   RO          195    .text               stm32f10x_rcc.o
    0x08000bec   0x08000bec   0x00000408   Code   RO          209    .text               stm32f10x_usart.o
    0x08000ff4   0x08000ff4   0x000000d0   Code   RO          431    .text               delay.o
    0x080010c4   0x080010c4   0x0000031c   Code   RO          446    .text               sys.o
    0x080013e0   0x080013e0   0x0000018c   Code   RO          461    .text               usart.o
    0x0800156c   0x0800156c   0x00000094   Code   RO          487    .text               main.o
    0x08001600   0x08001600   0x000006b8   Code   RO          509    .text               ad9959.o
    0x08001cb8   0x08001cb8   0x00000002   Code   RO          526    .text               c_w.l(use_no_semi_2.o)
    0x08001cba   0x08001cba   0x00000002   PAD
    0x08001cbc   0x08001cbc   0x00000028   Code   RO          530    .text               c_w.l(noretval__2sprintf.o)
    0x08001ce4   0x08001ce4   0x0000004e   Code   RO          534    .text               c_w.l(_printf_pad.o)
    0x08001d32   0x08001d32   0x00000002   PAD
    0x08001d34   0x08001d34   0x00000058   Code   RO          539    .text               c_w.l(_printf_hex_int.o)
    0x08001d8c   0x08001d8c   0x00000138   Code   RO          569    .text               c_w.l(__printf_flags_wp.o)
    0x08001ec4   0x08001ec4   0x00000006   Code   RO          579    .text               c_w.l(heapauxi.o)
    0x08001eca   0x08001eca   0x00000002   Code   RO          595    .text               c_w.l(use_no_semi.o)
    0x08001ecc   0x08001ecc   0x000000b2   Code   RO          598    .text               c_w.l(_printf_intcommon.o)
    0x08001f7e   0x08001f7e   0x00000002   PAD
    0x08001f80   0x08001f80   0x00000030   Code   RO          600    .text               c_w.l(_printf_char_common.o)
    0x08001fb0   0x08001fb0   0x0000000a   Code   RO          602    .text               c_w.l(_sputc.o)
    0x08001fba   0x08001fba   0x0000004a   Code   RO          627    .text               c_w.l(sys_stackheap_outer.o)
    0x08002004   0x08002004   0x00000012   Code   RO          629    .text               c_w.l(exit.o)
    0x08002016   0x08002016   0x00000002   PAD
    0x08002018   0x08002018   0x00000008   Code   RO          637    .text               c_w.l(libspace.o)
    0x08002020   0x08002020   0x0000000e   Code   RO          567    i._is_digit         c_w.l(__printf_wp.o)
    0x0800202e   0x0800202e   0x00000002   PAD
    0x08002030   0x08002030   0x0000005a   Code   RO          583    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x0800208a   0x0800208a   0x00000026   Code   RO          587    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x080020b0   0x080020b0   0x00000154   Code   RO          593    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08002204   0x08002204   0x0000009c   Code   RO          605    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080022a0   0x080022a0   0x0000000c   Code   RO          607    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080022ac   0x080022ac   0x00000000   Code   RO          609    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080022ac   0x080022ac   0x00000028   Data   RO          540    .constdata          c_w.l(_printf_hex_int.o)
    0x080022d4   0x080022d4   0x00000011   Data   RO          570    .constdata          c_w.l(__printf_flags_wp.o)
    0x080022e5   0x080022e5   0x00000003   PAD
    0x080022e8   0x080022e8   0x00000020   Data   RO          760    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002308, Size: 0x000006f0, Max: 0x0000c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002308   0x00000014   Data   RW          120    .data               system_stm32f10x.o
    0x20000014   0x0800231c   0x00000014   Data   RW          196    .data               stm32f10x_rcc.o
    0x20000028   0x08002330   0x00000004   Data   RW          432    .data               delay.o
    0x2000002c   0x08002334   0x00000006   Data   RW          463    .data               usart.o
    0x20000032   0x0800233a   0x00000006   PAD
    0x20000038   0x08002340   0x00000013   Data   RW          510    .data               ad9959.o
    0x2000004b   0x08002353   0x00000001   PAD
    0x2000004c        -       0x0000003f   Zero   RW          462    .bss                usart.o
    0x2000008b   0x08002353   0x00000001   PAD
    0x2000008c        -       0x00000060   Zero   RW          638    .bss                c_w.l(libspace.o)
    0x200000ec   0x08002353   0x00000004   PAD
    0x200000f0        -       0x00000200   Zero   RW          138    HEAP                startup_stm32f10x_hd.o
    0x200002f0        -       0x00000400   Zero   RW          137    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1720         52          0         19          0       9127   ad9959.o
         0          0          0          0          0       4588   core_cm3.o
       208          8          0          4          0       1087   delay.o
       148          4          0          0          0        495   main.o
       220         22          0          0          0       1833   misc.o
        64         26        304          0       1536        828   startup_stm32f10x_hd.o
       860         38          0          0          0       5761   stm32f10x_gpio.o
        26          0          0          0          0     229318   stm32f10x_it.o
       932         36          0         20          0       8956   stm32f10x_rcc.o
      1032         22          0          0          0       8492   stm32f10x_usart.o
       798         52          0          0          0       3780   sys.o
       480         38          0         20          0       1863   system_stm32f10x.o
       396         16          0          6         63       3228   usart.o

    ----------------------------------------------------------------------
      6884        <USER>        <GROUP>         76       1600     279356   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0          7          1          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       312          4         17          0          0         92   __printf_flags_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        40          6          0          0          0         84   noretval__2sprintf.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        90          4          0          0          0         92   dfixu.o
        38          0          0          0          0         68   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      1688         <USER>         <GROUP>          0        100       1700   Library Totals
        12          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1040         32         57          0         96       1276   c_w.l
       636         20          0          0          0        424   fz_ws.l

    ----------------------------------------------------------------------
      1688         <USER>         <GROUP>          0        100       1700   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      8572        366        396         76       1700     279180   Grand Totals
      8572        366        396         76       1700     279180   ELF Image Totals
      8572        366        396         76          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8968 (   8.76kB)
    Total RW  Size (RW Data + ZI Data)              1776 (   1.73kB)
    Total ROM Size (Code + RO Data + RW Data)       9044 (   8.83kB)

==============================================================================

