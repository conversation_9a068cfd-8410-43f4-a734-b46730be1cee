/**
  ******************************************************************************
  * @file    stm32f4xx_gpio.c
  * <AUTHOR> Application Team  
  * @version V1.8.0
  * @date    04-November-2016
  * @brief   This file provides firmware functions to manage the following 
  *          functionalities of the GPIO peripheral:
  *           + Initialization and Configuration
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x.h"

/* 声明STM32F1的原始GPIO_Init函数 */
extern void GPIO_Init_Original(GPIO_TypeDef* GPIOx, GPIO_InitTypeDef* GPIO_InitStruct);

/**
  * @brief  STM32F4风格的GPIO初始化函数，兼容STM32F1硬件
  * @param  GPIOx: GPIO端口
  * @param  GPIO_InitStruct: GPIO初始化结构体指针
  * @retval None
  */
void GPIO_Init_F4_Style(GPIO_TypeDef* GPIOx, GPIO_InitTypeDef* GPIO_InitStruct)
{
  /* 创建STM32F1风格的GPIO配置结构体 */
  GPIO_InitTypeDef GPIO_InitStructure_F1;

  /* 复制引脚定义和速度 */
  GPIO_InitStructure_F1.GPIO_Pin = GPIO_InitStruct->GPIO_Pin;
  GPIO_InitStructure_F1.GPIO_Speed = (GPIOSpeed_TypeDef)GPIO_InitStruct->GPIO_Speed;

  /* 根据STM32F4的模式和类型转换为STM32F1的模式 */
  if(GPIO_InitStruct->GPIO_Mode == GPIO_Mode_OUT)
  {
    if(GPIO_InitStruct->GPIO_OType == GPIO_OType_PP)
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_Out_PP;  /* 推挽输出 */
    else
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_Out_OD;  /* 开漏输出 */
  }
  else if(GPIO_InitStruct->GPIO_Mode == GPIO_Mode_IN)
  {
    if(GPIO_InitStruct->GPIO_PuPd == GPIO_PuPd_UP)
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_IPU;     /* 上拉输入 */
    else if(GPIO_InitStruct->GPIO_PuPd == GPIO_PuPd_DOWN)
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_IPD;     /* 下拉输入 */
    else
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_IN_FLOATING; /* 浮空输入 */
  }
  else if(GPIO_InitStruct->GPIO_Mode == GPIO_Mode_AF)
  {
    if(GPIO_InitStruct->GPIO_OType == GPIO_OType_PP)
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_AF_PP;   /* 复用推挽 */
    else
      GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_AF_OD;   /* 复用开漏 */
  }
  else if(GPIO_InitStruct->GPIO_Mode == GPIO_Mode_AN)
  {
    GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_AIN;       /* 模拟输入 */
  }
  else
  {
    GPIO_InitStructure_F1.GPIO_Mode = GPIO_Mode_IN_FLOATING; /* 默认浮空输入 */
  }

  /* 直接调用STM32F1的GPIO初始化函数 */
  GPIO_Init(GPIOx, &GPIO_InitStructure_F1);
}
