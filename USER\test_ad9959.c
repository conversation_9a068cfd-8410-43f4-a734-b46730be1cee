/**********************************************************
                    AD9959测试程序
功能：测试STM32F407ZGT6驱动AD9959模块的基本功能
作者：移植版本
时间：2025年
**********************************************************/

#include "stm32_config.h"
#include "stdio.h"
#include "AD9959.h"

/**********************************************************
** 函数名称：void Test_AD9959_Basic(void)
** 函数功能：测试AD9959基本功能
** 入口参数：无
** 出口参数：无
** 函数说明：测试四通道基本输出功能
**********************************************************/
void Test_AD9959_Basic(void)
{
    // 测试基本频率设置
    AD9959_Set_Fre(CH0, 1000000);   // 通道0: 1MHz
    AD9959_Set_Fre(CH1, 2000000);   // 通道1: 2MHz
    AD9959_Set_Fre(CH2, 5000000);   // 通道2: 5MHz
    AD9959_Set_Fre(CH3, 10000000);  // 通道3: 10MHz
    
    // 测试幅度设置
    AD9959_Set_Amp(CH0, 1023);      // 通道0: 最大幅度
    AD9959_Set_Amp(CH1, 800);       // 通道1: 约78%幅度
    AD9959_Set_Amp(CH2, 600);       // 通道2: 约59%幅度
    AD9959_Set_Amp(CH3, 400);       // 通道3: 约39%幅度
    
    // 测试相位设置
    AD9959_Set_Phase(CH0, 0);       // 通道0: 0°
    AD9959_Set_Phase(CH1, 4096);    // 通道1: 90°
    AD9959_Set_Phase(CH2, 8192);    // 通道2: 180°
    AD9959_Set_Phase(CH3, 12288);   // 通道3: 270°
    
    IO_Update();  // 更新输出
}

/**********************************************************
** 函数名称：void Test_AD9959_Sweep(void)
** 函数功能：测试AD9959扫频功能
** 入口参数：无
** 出口参数：无
** 函数说明：测试频率扫描功能
**********************************************************/
void Test_AD9959_Sweep(void)
{
    // 通道0进行频率扫描：从1MHz扫描到10MHz
    AD9959_Modulation_Init(CH0, DISABLE_Mod, SWEEP_ENABLE, LEVEL_MOD_2);
    AD9959_SetFre_Sweep(CH0, 1000000, 10000000, 100000, 100000, 10, 10, 1023, 0);
    IO_Update();
}

/**********************************************************
** 函数名称：void Test_AD9959_FSK(void)
** 函数功能：测试AD9959 FSK调制功能
** 入口参数：无
** 出口参数：无
** 函数说明：测试频移键控功能
**********************************************************/
void Test_AD9959_FSK(void)
{
    uint32_t fsk_freq[16] = {
        1000000, 2000000, 3000000, 4000000,
        5000000, 6000000, 7000000, 8000000,
        9000000, 10000000, 11000000, 12000000,
        13000000, 14000000, 15000000, 16000000
    };
    
    // 通道1进行FSK调制
    AD9959_Modulation_Init(CH1, FSK, SWEEP_DISABLE, LEVEL_MOD_2);
    AD9959_SetFSK(CH1, fsk_freq, 0);
    IO_Update();
}

/**********************************************************
** 函数名称：void Test_AD9959_ASK(void)
** 函数功能：测试AD9959 ASK调制功能
** 入口参数：无
** 出口参数：无
** 函数说明：测试幅移键控功能
**********************************************************/
void Test_AD9959_ASK(void)
{
    uint16_t ask_amp[16] = {
        100, 200, 300, 400, 500, 600, 700, 800,
        900, 1000, 900, 800, 700, 600, 500, 400
    };
    
    // 通道2进行ASK调制
    AD9959_Modulation_Init(CH2, ASK, SWEEP_DISABLE, LEVEL_MOD_2);
    AD9959_SetASK(CH2, ask_amp, 5000000, 0);
    IO_Update();
}

/**********************************************************
** 函数名称：void Test_AD9959_PSK(void)
** 函数功能：测试AD9959 PSK调制功能
** 入口参数：无
** 出口参数：无
** 函数说明：测试相移键控功能
**********************************************************/
void Test_AD9959_PSK(void)
{
    uint16_t psk_phase[16] = {
        0, 1024, 2048, 3072, 4096, 5120, 6144, 7168,
        8192, 9216, 10240, 11264, 12288, 13312, 14336, 15360
    };
    
    // 通道3进行PSK调制
    AD9959_Modulation_Init(CH3, PSK, SWEEP_DISABLE, LEVEL_MOD_2);
    AD9959_SetPSK(CH3, psk_phase, 8000000);
    IO_Update();
}

/**********************************************************
** 函数名称：void Test_AD9959_All(void)
** 函数功能：综合测试AD9959所有功能
** 入口参数：无
** 出口参数：无
** 函数说明：依次测试各种功能
**********************************************************/
void Test_AD9959_All(void)
{
    // 基本功能测试
    Test_AD9959_Basic();
    delay_ms(2000);  // 延时2秒观察输出
    
    // 扫频功能测试
    Test_AD9959_Sweep();
    delay_ms(3000);  // 延时3秒观察扫频
    
    // FSK调制测试
    Test_AD9959_FSK();
    delay_ms(2000);  // 延时2秒观察FSK
    
    // ASK调制测试
    Test_AD9959_ASK();
    delay_ms(2000);  // 延时2秒观察ASK
    
    // PSK调制测试
    Test_AD9959_PSK();
    delay_ms(2000);  // 延时2秒观察PSK
    
    // 回到基本输出模式
    Test_AD9959_Basic();
}

/**********************************************************
** 函数名称：int main_test(void)
** 函数功能：测试主函数
** 入口参数：无
** 出口参数：无
** 函数说明：可以替换main.c中的main函数进行测试
**********************************************************/
int main_test(void)
{
    MY_NVIC_PriorityGroup_Config(NVIC_PriorityGroup_2);  // 设置中断分组
    delay_init(168);  // 初始化延时函数，STM32F407默认168MHz
    delay_ms(500);    // 延时等待系统稳定
    
    AD9959_Init();    // 初始化AD9959
    
    while(1)
    {
        Test_AD9959_All();  // 循环测试所有功能
        delay_ms(5000);     // 每轮测试间隔5秒
    }
}
